import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { PoweredBy } from '@/libs/ui/PoweredBy/PoweredBy';
import { PromoItem } from './PromoItem/PromoItem';
import { PromoType } from '@/types/common';
import { PromoDetailsModal } from './PromoDetailsModal/PromoDetailsModal';
import { AddedToCart } from './AddedToCart/AddedToCart';
import { useQuery } from '@tanstack/react-query';
import { EstimatedRebatesPanelLoader } from '../EstimatedRebatesPanel/EstimatedRebatesPanelLoader';
import { fetchApi } from '@/libs/utils/api';
import { queryKeys } from '@/libs/query/queryClient';

export const PromoList = () => {
  const loadPromosFunc = async () => {
    const response = [
      {
        id: '0199058d-439e-73ac-80ae-eeb443b13014',
        name: 'Buy 1 get 1 FREE!',
        type: 'buy_x_get_y',
        description:
          'Buy 1 Credelio Quattro Chewable Tablets for Dogs, 56.25 mg, Yellow, 3.3 - 6 lb 10 x 6 Dose Box, get 1 FREE!',
        startedAt: '2025-09-01',
        endedAt: '2025-12-31',
        vendor: {
          id: '9d7559b3-2ab4-4966-8b8c-46e58c977923',
          name: 'Elanco',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/elanco-animal-health.png',
        },
        offers: [
          {
            id: '9e840706-235a-4dd2-8b43-6a906dd58cb8',
            name: 'Credelio Quattro Chewable Tablets for Dogs, 56.25 mg, Yellow, 3.3 - 6 lb 10 x 6 Dose Box',
            price: '1170.00',
            clinicPrice: '1170.00',
            gpoSavings: 0,
            vendorSavings: '0.00',
            isRecommended: false,
            isFavorite: true,
            lastOrderedAt: null,
            lastOrderedQuantity: null,
            stockStatus: 'IN_STOCK',
            vendorSku: '084416',
            increments: 1,
            size: null,
            unitOfMeasure: null,
            vendor: {
              id: '9d7559b3-0b71-4606-88b0-e903fc518846',
              name: 'Covetrus',
              imageUrl:
                'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
            },
            quantityInCart: 0,
          },
        ],
        requirements: [
          {
            type: 'minimum_quantity',
            description: 'Minimum quantity required',
            minimumQuantity: '1',
          },
        ],
        benefits: [
          {
            type: 'give_free_product',
            description: 'Buy X Get Y promotion',
            quantity: '1',
            message: 'You got your promoted products!',
            freeProductOffer: null,
          },
        ],
        keyword: 'promo:elanco-buy_x_get_y-buy-1-get-1-free!',
      },
      {
        id: '0197d1ac-cb1f-7055-be17-c1d37e56ffb5',
        name: 'Buy 1 Simparica 3 x 5mg Gold 2.8 - 5.5lbs, get 1 for Free',
        type: 'buy_x_get_y',
        description: 'Buy 1 Simparica Gold, get 1 for Free',
        startedAt: '2025-07-01',
        endedAt: '2025-09-30',
        vendor: {
          id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
          name: 'Zoetis',
          imageUrl:
            'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
        },
        offers: [
          {
            id: '9d7581c7-e947-40f0-adb7-471859462816',
            name: 'Simparica 3 x 5mg Gold 2.8 - 5.5lbs',
            price: '41.55',
            clinicPrice: null,
            gpoSavings: 0,
            vendorSavings: '0.00',
            isRecommended: false,
            isFavorite: false,
            lastOrderedAt: '2025-08-05T17:09:10.000000Z',
            lastOrderedQuantity: 10,
            stockStatus: 'IN_STOCK',
            vendorSku: '10012449',
            increments: 1,
            size: null,
            unitOfMeasure: null,
            vendor: {
              id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
              name: 'Zoetis',
              imageUrl:
                'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
            },
            quantityInCart: 0,
          },
        ],
        requirements: [
          {
            type: 'minimum_quantity',
            description: 'Minimum quantity required',
            minimumQuantity: '1',
          },
        ],
        benefits: [
          {
            type: 'give_free_product',
            description: 'Buy X Get Y promotion',
            quantity: '1',
            message: null,
            freeProductOffer: {
              id: '9d7581c7-e947-40f0-adb7-471859462816',
              name: 'Simparica 3 x 5mg Gold 2.8 - 5.5lbs',
              price: '41.55',
              clinicPrice: null,
              gpoSavings: 0,
              vendorSavings: '0.00',
              isRecommended: false,
              isFavorite: false,
              lastOrderedAt: '2025-08-05T17:09:10.000000Z',
              lastOrderedQuantity: 10,
              stockStatus: 'IN_STOCK',
              vendorSku: '10012449',
              increments: 1,
              size: null,
              unitOfMeasure: null,
              vendor: {
                id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                name: 'Zoetis',
                imageUrl:
                  'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
              },
              quantityInCart: 0,
            },
          },
        ],
        keyword:
          'promo:zoetis-buy_x_get_y-buy-1-simparica-3-x-5mg-gold-2.8---5.5lbs,-get-1-for-free',
      },
    ];
    return response;
  };

  const { data: promos, isLoading } = useQuery({
    queryKey: queryKeys.promotions.buyXGetY(),
    queryFn: loadPromosFunc,
  });

  if (isLoading) return <EstimatedRebatesPanelLoader />;

  if (!promos || promos.length === 0) return null;

  return (
    <>
      <CollapsiblePanel
        header={
          <>
            <h3 className="py-[1.3rem] pr-4 pl-6 text-xl font-medium">
              Promo Matcher
            </h3>
            <PoweredBy className="relative h-4 text-sm" />
          </>
        }
        content={
          <div className="flex flex-col">
            <div className="flex-col bg-white p-6">
              <h3 className="text-lg font-medium">Handpicked Deals for You!</h3>
              <p className="mb-8 text-sm text-black/80">
                We found the best deals for you - don&apos;t wait!
              </p>
              <div className="space-y-4 rounded-sm bg-black/2 p-4">
                {promos.map((promo) => (
                  <PromoItem key={promo.id} {...promo} />
                ))}
              </div>
            </div>
          </div>
        }
        startOpen
      />
      <PromoDetailsModal />
      <AddedToCart />
    </>
  );
};
